<script setup>
import { ref } from 'vue'
import UploadProgress from './UploadProgress.vue'

const files = ref([
  {
    id: 1,
    fileName: '20250701XX法院诉讼文件.pdf',
    fileSize: '3.2 MB',
    progress: 25,
    status: 'uploading',
    similarCases: [],
  },
  {
    id: 2,
    fileName: '20250701XX法院诉讼文件.pdf',
    fileSize: '3.2 MB',
    progress: 100,
    status: 'completed',
    similarCases: [],
  },
  {
    id: 3,
    fileName: '20250701XX法院诉讼文件.pdf',
    fileSize: '3.2 MB',
    progress: 50,
    status: 'analyzing',
    similarCases: [
      { name: 'XXXX法院诉讼文件.pdf' },
      { name: 'XXXX法院诉讼文件.pdf' },
      { name: 'XXXX法院诉讼文件.pdf' },
      { name: '法院诉讼文件.pdf' },
      { name: '法院诉讼文件.pdf' },
    ],
  },
  {
    id: 4,
    fileName: '20250701XX法院诉讼文件.pdf',
    fileSize: '3.2 MB',
    progress: 100,
    status: 'analyzed',
    similarCases: [
      { name: 'XXXX法院诉讼文件.pdf' },
      { name: 'XXXX法院诉讼文件.pdf' },
      { name: 'XXXX法院诉讼文件.pdf' },
      { name: '法院诉讼文件.pdf' },
      { name: '法院诉讼文件.pdf' },
    ],
  },
])
</script>

<template>
  <div class="file-upload-container">
    <h1 class="title fixed-header">文书检查</h1>
    <div class="upload-box fixed-header">
      <img class="upload-icon" src="@/assets/icons/upload-icon.png" alt="Upload Icon" />
      <div class="upload-text">
        <p>
          <span>将文件拖到此处，或 </span>
          <a class="upload-link">点击上传</a>
        </p>
        <p class="supported-formats">支持 doc、docx、pdf、txt 格式</p>
      </div>
    </div>

    <div class="upload-list scroll-container">
      <UploadProgress
        v-for="file in files"
        :key="file.id"
        :file-name="file.fileName"
        :file-size="file.fileSize"
        :progress="file.progress"
        :status="file.status"
        :similar-cases="file.similarCases"
      />
    </div>
  </div>
</template>

<style scoped>
.file-upload-container {
  flex-grow: 1;
  padding: 24px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.title {
  font-size: 16px;
  font-family: 'HarmonyOS Sans', sans-serif;
  font-weight: 500;
  color: #222529;
  margin: 0 0 20px 0;
}

.upload-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 194px;
  border: 1px dashed #bfccdb;
  border-radius: 4px;
  background-color: #ffffff;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-box:hover {
  border-color: #0057d9;
  background-color: #f8f9fa;
}

.upload-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  transition: transform 0.3s ease;
}

.upload-box:hover .upload-icon {
  transform: scale(1.05);
}

.upload-text p {
  margin: 0;
  font-size: 14px;
  font-family: 'HarmonyOS Sans', sans-serif;
  line-height: 1.5;
}

.upload-link {
  color: #0057d9;
  font-weight: 500;
  text-decoration: none;
  transition: text-decoration 0.3s ease;
}

.upload-link:hover {
  text-decoration: underline;
}

.supported-formats {
  color: #7f8792;
  margin-top: 16px !important;
}

.upload-list {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
</style>
