<template>
  <nav class="menu">
    <router-link
      v-for="menuRoute in menuRoutes"
      :key="menuRoute.path"
      :to="menuRoute.path"
      custom
      v-slot="{ navigate, href, isActive }"
    >
      <a
        :href="href"
        @click="navigate"
        class="menu-item"
        :class="{ active: isActive }"
      >
        <div class="menu-icon-wrapper">
          <div class="icon-border">
            <img class="menu-icon" :src="getIcon(menuRoute.meta.icon)" />
          </div>
        </div>
        <span style="font-weight: 500">{{ menuRoute.meta.title }}</span>
      </a>
    </router-link>
  </nav>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const getIcon = (name) => {
  return new URL(`../../assets/icons/${name}`, import.meta.url).href
}

const menuRoutes = computed(() =>
  router.options.routes.find((r) => r.path === '/').children.filter((r) => r.meta && r.meta.title)
)
</script>

<style scoped>
.menu {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex-grow: 1; /* Allow menu to grow and push user info to bottom */
  overflow-y: auto; /* Add scroll for long menus */
}

.menu-item {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 10px;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'HarmonyOS Sans', sans-serif;
  font-weight: 500;
  color: #222529;
  text-decoration: none;
  transition:
    background-color 0.3s,
    color 0.3s;
  flex-shrink: 0; /* Prevent items from shrinking */
}

.menu-item.active {
  background-color: #e5f0ff;
  color: #0057d9;
}

.menu-item:not(.active):hover {
  background-color: #f0f1f2;
}

.menu-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 10px;
}

.icon-border {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  border-radius: 2px;
}

.menu-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.menu-item:hover .menu-icon {
  transform: scale(1.1);
}

.menu-item.active .icon-border {
  border-color: #0057d9;
}
</style>
