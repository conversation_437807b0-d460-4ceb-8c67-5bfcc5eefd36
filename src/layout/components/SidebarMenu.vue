<template>
  <el-menu :default-active="activeMenu" class="sidebar-menu" @select="handleMenuSelect">
    <template v-for="menuRoute in menuRoutes" :key="menuRoute.path">
      <!-- 有子菜单的菜单项 -->
      <el-sub-menu
        v-if="menuRoute.meta.submenu && menuRoute.meta.submenu.length > 0"
        :index="menuRoute.path"
      >
        <template #title>
          <img class="menu-icon" :src="getIcon(menuRoute.meta.icon)" />
          <span>{{ menuRoute.meta.title }}</span>
        </template>
        <el-menu-item
          v-for="subItem in menuRoute.meta.submenu"
          :key="subItem.type"
          :index="`${menuRoute.path}?type=${subItem.type}`"
        >
          {{ subItem.title }}
        </el-menu-item>
      </el-sub-menu>

      <!-- 没有子菜单的菜单项 -->
      <el-menu-item v-else :index="menuRoute.path">
        <img class="menu-icon" :src="getIcon(menuRoute.meta.icon)" />
        <span>{{ menuRoute.meta.title }}</span>
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const getIcon = (name) => {
  return new URL(`../../assets/icons/${name}`, import.meta.url).href
}

const menuRoutes = computed(() =>
  router.options.routes.find((r) => r.path === '/').children.filter((r) => r.meta && r.meta.title),
)

// 计算当前激活的菜单项
const activeMenu = computed(() => {
  const currentPath = route.path.replace(/^\//, '') // 移除开头的斜杠
  const queryType = route.query.type

  let result
  if (queryType !== undefined) {
    // 如果有查询参数，返回完整的路径+查询参数
    result = `${currentPath}?type=${queryType}`
  } else {
    result = currentPath
  }

  console.log('当前激活菜单:', {
    routePath: route.path,
    queryType: route.query.type,
    currentPath,
    activeMenu: result,
  })

  return result
})

// 处理菜单选择
const handleMenuSelect = (index) => {
  if (index.includes('?')) {
    // 包含查询参数的路径
    const [path, query] = index.split('?')
    const params = new URLSearchParams(query)
    const queryObj = {}
    for (const [key, value] of params) {
      queryObj[key] = value
    }
    router.push({ path, query: queryObj })
  } else {
    // 普通路径
    router.push(index)
  }
}
</script>

<style scoped>
.sidebar-menu {
  margin-top: 20px;
  border: none;
  background: transparent;
  flex-grow: 1;
  overflow-y: auto;
}

/* 重置Element Plus菜单的默认样式 */
.sidebar-menu :deep(.el-menu-item),
.sidebar-menu :deep(.el-sub-menu__title) {
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  margin-bottom: 16px;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'HarmonyOS Sans', sans-serif;
  font-weight: 500;
  color: #222529;
  background: transparent;
  border: none;
}

/* 激活状态 */
.sidebar-menu :deep(.el-menu-item.is-active),
.sidebar-menu :deep(.el-sub-menu.is-active > .el-sub-menu__title) {
  background-color: #e5f0ff !important;
  color: #0057d9 !important;
}

/* 悬停状态 */
.sidebar-menu :deep(.el-menu-item:hover),
.sidebar-menu :deep(.el-sub-menu__title:hover) {
  background-color: #f0f1f2 !important;
  color: #222529;
}

/* 菜单图标 */
.menu-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  transition: transform 0.3s ease;
}

.sidebar-menu :deep(.el-menu-item:hover) .menu-icon,
.sidebar-menu :deep(.el-sub-menu__title:hover) .menu-icon {
  transform: scale(1.1);
}

/* 子菜单样式 */
.sidebar-menu :deep(.el-menu--inline) {
  background: transparent;
}

.sidebar-menu :deep(.el-menu--inline .el-menu-item) {
  height: 32px;
  line-height: 32px;
  padding-left: 38px;
  margin-bottom: 4px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 400;
  color: #666;
  background: transparent;
}

.sidebar-menu :deep(.el-menu--inline .el-menu-item.is-active) {
  background-color: #e5f0ff !important;
  color: #0057d9 !important;
  font-weight: 500;
}

.sidebar-menu :deep(.el-menu--inline .el-menu-item:hover) {
  background-color: #f8f9fa !important;
  color: #222529;
}

/* 展开箭头样式 */
.sidebar-menu :deep(.el-sub-menu__icon-arrow) {
  margin-left: auto;
  transition: transform 0.3s ease;
}

/* 移除默认的边框和阴影 */
.sidebar-menu :deep(.el-menu-item),
.sidebar-menu :deep(.el-sub-menu__title),
.sidebar-menu :deep(.el-menu--inline .el-menu-item) {
  border: none !important;
  box-shadow: none !important;
}

/* 调整子菜单的缩进 */
.sidebar-menu :deep(.el-sub-menu .el-menu) {
  background: transparent;
}
</style>
