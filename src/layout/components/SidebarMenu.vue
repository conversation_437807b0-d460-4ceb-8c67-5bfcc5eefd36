<template>
  <nav class="menu">
    <template v-for="menuRoute in menuRoutes" :key="menuRoute.path">
      <!-- 主菜单项 -->
      <router-link :to="menuRoute.path" custom v-slot="{ navigate, href, isActive }">
        <a
          :href="href"
          @click="navigate"
          class="menu-item"
          :class="{ active: isActive || isParentActive(menuRoute) }"
        >
          <div class="menu-icon-wrapper">
            <div class="icon-border">
              <img class="menu-icon" :src="getIcon(menuRoute.meta.icon)" />
            </div>
          </div>
          <span style="font-weight: 500">{{ menuRoute.meta.title }}</span>
        </a>
      </router-link>

      <!-- 子菜单项 -->
      <div v-if="menuRoute.children && menuRoute.children.length > 0" class="submenu">
        <router-link
          v-for="subRoute in menuRoute.children"
          :key="subRoute.path"
          :to="`${menuRoute.path}/${subRoute.path}`"
          custom
          v-slot="{ navigate, href, isActive }"
        >
          <a :href="href" @click="navigate" class="submenu-item" :class="{ active: isActive }">
            <span>{{ subRoute.meta.title }}</span>
          </a>
        </router-link>
      </div>
    </template>
  </nav>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const getIcon = (name) => {
  return new URL(`../../assets/icons/${name}`, import.meta.url).href
}

const menuRoutes = computed(() =>
  router.options.routes.find((r) => r.path === '/').children.filter((r) => r.meta && r.meta.title),
)

// 检查父菜单是否处于激活状态（当子菜单激活时）
const isParentActive = (menuRoute) => {
  if (!menuRoute.children || menuRoute.children.length === 0) return false
  return menuRoute.children.some((child) => route.path === `${menuRoute.path}/${child.path}`)
}
</script>

<style scoped>
.menu {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex-grow: 1; /* Allow menu to grow and push user info to bottom */
  overflow-y: auto; /* Add scroll for long menus */
}

.menu-item {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 10px;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'HarmonyOS Sans', sans-serif;
  font-weight: 500;
  color: #222529;
  text-decoration: none;
  transition:
    background-color 0.3s,
    color 0.3s;
  flex-shrink: 0; /* Prevent items from shrinking */
}

.menu-item.active {
  background-color: #e5f0ff;
  color: #0057d9;
}

.menu-item:not(.active):hover {
  background-color: #f0f1f2;
}

.menu-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 10px;
}

.icon-border {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  border-radius: 2px;
}

.menu-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.menu-item:hover .menu-icon {
  transform: scale(1.1);
}

.menu-item.active .icon-border {
  border-color: #0057d9;
}

/* 子菜单样式 */
.submenu {
  margin-left: 26px;
  margin-top: 8px;
  margin-bottom: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.submenu-item {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 0 12px;
  border-radius: 6px;
  font-size: 13px;
  font-family: 'HarmonyOS Sans', sans-serif;
  font-weight: 400;
  color: #666;
  text-decoration: none;
  transition:
    background-color 0.3s,
    color 0.3s;
  flex-shrink: 0;
}

.submenu-item.active {
  background-color: #e5f0ff;
  color: #0057d9;
  font-weight: 500;
}

.submenu-item:not(.active):hover {
  background-color: #f8f9fa;
  color: #222529;
}
</style>
